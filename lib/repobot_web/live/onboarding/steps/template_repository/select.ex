defmodule RepobotWeb.Live.Onboarding.Steps.TemplateRepository.Select do
  use RepobotWeb.Live.Onboarding.Step

  def update(assigns, socket) do
    {:ok,
     socket
     |> assign(assigns)
     |> assign_repositories()
     |> assign_new(:selected_template_repo_id, fn -> nil end)
     |> assign_new(:template_search_term, fn -> "" end)
     |> assign_new(:template_repo, fn -> nil end)}
  end

  def render(assigns) do
    ~H"""
    <div id={@id} class="space-y-4">
      <fieldset class="fieldset">
        <label for="template_search" class="label">
          Search Existing Repositories
        </label>
        <input
          type="text"
          name="template_search"
          id="template_search"
          class="input"
          placeholder="Filter by name..."
          value={@template_search_term}
          phx-keyup="search"
          phx-debounce="300"
          phx-target={@myself}
        />
      </fieldset>

      <div class="space-y-6 max-h-[400px] overflow-y-auto border border-slate-200 rounded-md p-4">
        <%= if Enum.any?(@unorganized_repos) do %>
          <div class="bg-white rounded-lg border border-slate-200 overflow-hidden">
            <div class="bg-slate-50 px-4 py-2 border-b border-slate-200">
              <h3 class="text-sm font-medium text-slate-600">
                Unorganized Repositories
              </h3>
            </div>
            <div class="divide-y divide-slate-200">
              <%= for repo <- @unorganized_repos do %>
                <label
                  for={"select_repo_" <> repo.id}
                  class="px-4 py-3 flex items-center gap-3 hover:bg-slate-50 cursor-pointer"
                  data-repo-id={repo.id}
                >
                  <input
                    type="radio"
                    name="selected_template_repo_id"
                    value={repo.id}
                    id={"select_repo_" <> repo.id}
                    checked={@selected_template_repo_id == repo.id}
                    phx-click="select_repository"
                    phx-target={@myself}
                    class="h-4 w-4 border-slate-300 text-indigo-600 focus:ring-indigo-600"
                  />
                  <span class="truncate font-medium text-slate-800">
                    {repo.full_name}
                  </span>
                  <%= if repo.fork do %>
                    <.icon name="hero-arrow-path" class="w-4 h-4 text-slate-400" />
                  <% end %>
                  <%= if repo.template do %>
                    <span class="ml-auto inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-emerald-100 text-emerald-800">
                      Template
                    </span>
                  <% end %>
                </label>
              <% end %>
            </div>
          </div>
        <% end %>

        <%= for folder <- Enum.sort_by(@folders, & &1.name) do %>
          <% folder_repos =
            Map.get(@repos_by_folder, folder.id, []) |> Enum.sort_by(&{&1.fork, &1.full_name}) %>
          <%= if Enum.any?(folder_repos) do %>
            <div class="bg-white rounded-lg border border-slate-200 overflow-hidden">
              <div class="bg-slate-50 px-4 py-2 border-b border-slate-200">
                <h3 class="text-sm font-medium text-slate-600 flex items-center gap-2">
                  <.icon name="hero-folder" class="w-4 h-4 text-slate-400" />
                  {folder.name}
                </h3>
              </div>
              <div class="divide-y divide-slate-200">
                <%= for repo <- folder_repos do %>
                  <label
                    for={"select_repo_" <> repo.id}
                    class="px-4 py-3 flex items-center gap-3 hover:bg-slate-50 cursor-pointer"
                    data-repo-id={repo.id}
                  >
                    <input
                      type="radio"
                      name="selected_template_repo_id"
                      value={repo.id}
                      id={"select_repo_" <> repo.id}
                      checked={@selected_template_repo_id == repo.id}
                      phx-click="select_repository"
                      phx-target={@myself}
                      class="h-4 w-4 border-slate-300 text-indigo-600 focus:ring-indigo-600"
                    />
                    <span class="truncate font-medium text-slate-800">
                      {repo.full_name}
                    </span>
                    <%= if repo.fork do %>
                      <.icon name="hero-arrow-path" class="w-4 h-4 text-slate-400" />
                    <% end %>
                    <%= if repo.template do %>
                      <span class="ml-auto inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-emerald-100 text-emerald-800">
                        Template
                      </span>
                    <% end %>
                  </label>
                <% end %>
              </div>
            </div>
          <% end %>
        <% end %>
      </div>
    </div>
    """
  end

  def handle_event("search", %{"value" => search_term}, socket) do
    socket =
      socket
      |> assign(:template_search_term, search_term)
      |> filter_repositories_by_search(search_term)

    {:noreply, socket}
  end

  def handle_event("select_repository", %{"value" => id}, socket) do
    repo = Enum.find(socket.assigns.repositories, &(&1.id == id))

    finalize(:template_repository, %{template_repo: repo})

    {:noreply, socket |> assign(:template_repo, repo) |> assign(:selected_template_repo_id, id)}
  end

  defp filter_repositories_by_search(socket, search_term) do
    if search_term == "" do
      # If search is empty, show all repositories
      assign_repositories(socket)
    else
      # Filter repositories by search term (case insensitive)
      search_term_lower = String.downcase(search_term)

      # Filter unorganized repos
      filtered_unorganized_repos =
        socket.assigns.unorganized_repos
        |> Enum.filter(fn repo ->
          String.contains?(String.downcase(repo.full_name), search_term_lower)
        end)

      # Filter repos by folder
      filtered_repos_by_folder =
        socket.assigns.repos_by_folder
        |> Enum.map(fn {folder_id, repos} ->
          filtered_repos =
            repos
            |> Enum.filter(fn repo ->
              String.contains?(String.downcase(repo.full_name), search_term_lower)
            end)

          {folder_id, filtered_repos}
        end)
        |> Enum.into(%{})

      socket
      |> assign(:unorganized_repos, filtered_unorganized_repos)
      |> assign(:repos_by_folder, filtered_repos_by_folder)
    end
  end
end
