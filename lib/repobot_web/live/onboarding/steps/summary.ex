defmodule RepobotWeb.Live.Onboarding.Steps.Summary do
  use RepobotWeb.Live.Onboarding.Step

  alias Repobot.{Accounts, SourceFiles}

  require Logger

  @impl true
  def update(assigns, socket) do
    socket =
      socket
      |> assign(assigns)
      |> assign_new(:finalizing_progress, fn -> 0 end)
      |> assign_new(:finalizing_status, fn -> nil end)
      |> assign_new(:finalized_template_repo, fn -> nil end)
      |> assign_new(:finalized_source_files, fn -> [] end)
      |> assign_new(:existing_files, fn -> [] end)
      |> assign_new(:new_files, fn -> [] end)
      |> assign_new(:github_commit_result, fn -> nil end)

    socket =
      cond do
        # Handle commit result first if present
        !is_nil(socket.assigns.github_commit_result) ->
          handle_commit_result(socket)
          # Clear the result after processing
          |> assign(:github_commit_result, nil)

        # Start finalization only on initial connect and if not already started/finished
        connected?(socket) && socket.assigns.finalizing_progress == 0 ->
          start_finalization(socket)

        # Otherwise, do nothing (e.g., reconnecting, already finalizing/finished)
        true ->
          socket
      end

    {:ok, socket}
  end

  defp handle_commit_result(socket) do
    case socket.assigns.github_commit_result do
      {:ok, newly_created_source_files, template_repo} ->
        # Get the DB SourceFile structs corresponding to the files selected that already existed
        existing_db_files_corresponding_to_selected =
          template_repo
          |> Repobot.Repo.preload(:source_files)
          |> Map.get(:source_files, [])
          |> Enum.filter(fn db_file ->
            Enum.any?(socket.assigns.existing_files, &(&1.path == db_file.target_path))
          end)

        all_source_files =
          existing_db_files_corresponding_to_selected ++ newly_created_source_files

        socket =
          socket
          |> assign(:finalizing_progress, 100)
          |> assign(:finalizing_status, "Template repository setup complete!")
          |> assign(:finalized_template_repo, template_repo)
          # Assign the combined list of DB structs
          |> assign(:finalized_source_files, all_source_files)

        # Mark onboarding as completed
        case mark_onboarding_completed(socket) do
          {:ok, user} ->
            finalize(:summary, %{
              finalized_template_repo: template_repo,
              # Use the combined list
              finalized_source_files: all_source_files,
              current_user: user
            })

          {:error, _changeset} ->
            Logger.error("Failed to mark onboarding as completed")
        end

        socket

      {:error, reason, _source_files, _template_repo} ->
        Logger.error("Failed to push files to GitHub: #{inspect(reason)}")
        # Optionally, display a more specific error if `reason` is structured
        status_message = if is_binary(reason), do: reason, else: "Failed to push files to GitHub."

        assign(socket, :finalizing_status, status_message)
        # Consider showing the error state in the UI as well
        # Set progress to 100 to stop spinner
        |> assign(:finalizing_progress, 100)
        # Indicate finalization failed
        |> assign(:finalized_template_repo, nil)

      nil ->
        # Should not happen if called correctly, but safe to handle
        socket
    end
  end

  defp start_finalization(socket) do
    # Get state safely, handle case where selected_files key doesn't exist
    state = socket.assigns.state
    # Use Map.get for safety
    selected_files = Map.get(state, :selected_files, [])
    template_repo = state.template_repo
    current_user = socket.assigns.current_user
    target_folders = Map.get(state, :target_folders, [])

    # First, ensure the repository is marked as a template and added to target folders
    template_repo =
      case ensure_repository_is_template(template_repo, target_folders) do
        {:ok, updated_repo} ->
          Logger.info("Successfully marked repository #{updated_repo.full_name} as template")
          updated_repo

        {:error, reason} ->
          Logger.error("Failed to mark repository as template: #{inspect(reason)}")
          # Continue with original repo, but this might cause issues
          template_repo
      end

    # Get the actual SourceFile structs already associated with the template repo in the DB
    existing_source_files_in_db =
      template_repo
      |> Repobot.Repo.preload(:source_files)
      |> Map.get(:source_files, [])

    # Split the selected files into those to add and those already existing (based on target_path)
    {selected_files_to_add, selected_files_already_exist} =
      Enum.split_with(selected_files, fn selected_file ->
        # Check if a source file with the same target path already exists in the DB for this template repo
        not Enum.any?(existing_source_files_in_db, &(&1.target_path == selected_file.path))
      end)

    # Get the actual SourceFile structs from the DB that correspond to the selected files that already exist
    # This is needed for the :finalized_source_files assign if no new files are added
    existing_db_files_corresponding_to_selected =
      Enum.filter(existing_source_files_in_db, fn db_file ->
        Enum.any?(selected_files_already_exist, &(&1.path == db_file.target_path))
      end)

    socket =
      socket
      # Assign the *selected file data* for display purposes
      |> assign(:existing_files, selected_files_already_exist)
      |> assign(:new_files, selected_files_to_add)

    if Enum.empty?(selected_files_to_add) do
      # All files already exist or no files were selected
      socket =
        socket
        |> assign(:finalizing_progress, 100)
        |> assign(
          :finalizing_status,
          if(Enum.empty?(selected_files),
            do: "No files were selected for the template.",
            else: "Template repository already has all selected files!"
          )
        )
        |> assign(:finalized_template_repo, template_repo)
        # Assign the actual SourceFile structs from the DB
        |> assign(:finalized_source_files, existing_db_files_corresponding_to_selected)

      # Mark onboarding as completed since we're done
      case mark_onboarding_completed(socket) do
        {:ok, user} ->
          finalize(:summary, %{
            finalized_template_repo: template_repo,
            # Use the actual SourceFile structs from the DB
            finalized_source_files: existing_db_files_corresponding_to_selected,
            current_user: user
          })

        {:error, _} ->
          Logger.error("Failed to mark onboarding as completed")
      end

      socket
    else
      # Pass the list of files to add (selected_files_to_add) to create_source_files
      # Also pass target folders to associate source files with them
      target_folders = Map.get(socket.assigns.state, :target_folders, [])

      case create_source_files(selected_files_to_add, template_repo, current_user, target_folders) do
        # These are the newly created SourceFile structs
        {:ok, newly_created_source_files} ->
          socket =
            socket
            |> assign(:finalizing_progress, 50)
            |> assign(:finalizing_status, "Pushing new files to GitHub...")

          # We will assign finalized_source_files in handle_commit_result
          # by combining newly_created_source_files and existing_db_files_corresponding_to_selected

          # Capture parent PID before starting the task
          parent_pid = self()

          # Start async GitHub operation
          Task.start(fn ->
            github_api = Application.get_env(:repobot, :github_api)
            # Use template repo owner/name from the struct
            client = github_api.client(template_repo.owner, template_repo.name)

            # Filter out files with nil content before creating the tree
            valid_source_files_for_tree =
              Enum.filter(newly_created_source_files, &(&1.content != nil))

            if Enum.empty?(valid_source_files_for_tree) do
              Logger.warning(
                "No valid source files with content found to create tree for #{template_repo.full_name}"
              )

              # Send an appropriate result back? Maybe :ok if nothing to commit?
              # Or maybe an error indicating no content?
              # For now, let's treat it as success (no commit needed for these files)
              send(
                parent_pid,
                {:github_commit_result, :ok, newly_created_source_files, template_repo}
              )
            else
              # Log if some files were filtered
              if length(valid_source_files_for_tree) != length(newly_created_source_files) do
                filtered_paths =
                  (newly_created_source_files -- valid_source_files_for_tree)
                  |> Enum.map(& &1.target_path)

                Logger.warning(
                  "Filtered out #{length(filtered_paths)} files with nil content before creating tree: #{inspect(filtered_paths)}"
                )
              end

              # Create tree with the VALID files only
              tree_items =
                Enum.map(
                  # Use the filtered list
                  valid_source_files_for_tree,
                  &%{
                    path: &1.target_path,
                    # Assuming standard file mode
                    mode: "100644",
                    type: "blob",
                    # Content is guaranteed not nil here
                    content: &1.content
                  }
                )

              # Fetch the latest commit to get the base tree SHA
              case github_api.get_ref(
                     client,
                     template_repo.owner,
                     template_repo.name,
                     "heads/#{template_repo.data["default_branch"]}"
                   ) do
                {200, %{"object" => %{"sha" => parent_sha}}, _} ->
                  case github_api.get_commit(
                         client,
                         template_repo.owner,
                         template_repo.name,
                         parent_sha
                       ) do
                    {200, %{"tree" => %{"sha" => base_tree_sha}}, _} ->
                      # Now create the tree using the base tree
                      case github_api.create_tree(
                             client,
                             template_repo.owner,
                             template_repo.name,
                             tree_items,
                             base_tree_sha
                           ) do
                        {201, %{"sha" => new_tree_sha}, _} ->
                          commit_message =
                            "Add #{length(newly_created_source_files)} template files"

                          result =
                            github_api.create_commit(
                              client,
                              template_repo.owner,
                              template_repo.name,
                              commit_message,
                              new_tree_sha,
                              [parent_sha]
                            )

                          case result do
                            {201, %{"sha" => commit_sha}, _} ->
                              case github_api.update_reference(
                                     client,
                                     template_repo.owner,
                                     template_repo.name,
                                     "heads/#{template_repo.data["default_branch"]}",
                                     commit_sha,
                                     false
                                   ) do
                                {200, _, _} ->
                                  # After successfully creating files in GitHub, refresh the template repository files in the database
                                  case Repobot.Repositories.refresh_repository_files!(
                                         template_repo.id,
                                         current_user
                                       ) do
                                    {:ok, refreshed_template_repo} ->
                                      # Send result to parent PID with refreshed repository
                                      send(
                                        parent_pid,
                                        {:github_commit_result, :ok, newly_created_source_files,
                                         refreshed_template_repo}
                                      )

                                    {:error, refresh_error} ->
                                      Logger.error(
                                        "Failed to refresh template repository files after GitHub commit: #{inspect(refresh_error)}"
                                      )

                                      # Send result with original repo, but log the refresh error
                                      send(
                                        parent_pid,
                                        {:github_commit_result, :ok, newly_created_source_files,
                                         template_repo}
                                      )
                                  end

                                {status, body, _} ->
                                  # Send result to parent PID
                                  send(
                                    parent_pid,
                                    {:github_commit_result,
                                     {:error,
                                      "Failed to update ref: #{status} - #{inspect(body)}"},
                                     newly_created_source_files, template_repo}
                                  )
                              end

                            {status, body, _} ->
                              # Send result to parent PID
                              send(
                                parent_pid,
                                {:github_commit_result,
                                 {:error,
                                  "Failed to create commit: #{status} - #{inspect(body)}"},
                                 newly_created_source_files, template_repo}
                              )
                          end

                        {status, body, _} ->
                          # Send result to parent PID
                          send(
                            parent_pid,
                            {:github_commit_result,
                             {:error, "Failed to create tree: #{status} - #{inspect(body)}"},
                             newly_created_source_files, template_repo}
                          )
                      end

                    {status, body, _} ->
                      # Send result to parent PID
                      send(
                        parent_pid,
                        {:github_commit_result,
                         {:error, "Failed to get base commit tree: #{status} - #{inspect(body)}"},
                         newly_created_source_files, template_repo}
                      )
                  end

                {status, body, _} ->
                  # Send result to parent PID
                  send(
                    parent_pid,
                    {:github_commit_result,
                     {:error, "Failed to get ref: #{status} - #{inspect(body)}"},
                     newly_created_source_files, template_repo}
                  )
              end
            end
          end)

          socket

        {:error, reason} ->
          Logger.error("Failed to create source files: #{inspect(reason)}")
          assign(socket, :finalizing_status, "Failed to save template file definitions.")
      end
    end
  end

  defp mark_onboarding_completed(socket) do
    current_user = socket.assigns.current_user
    settings = current_user.settings || %Accounts.User.Settings{}

    Accounts.update_user(current_user, %{
      settings: Map.from_struct(%{settings | onboarding_completed: true})
    })
  end

  defp create_source_files(selected_files, template_repo, current_user, target_folders) do
    Enum.reduce_while(selected_files, {:ok, []}, fn file, {:ok, acc} ->
      # file is %{path: ..., content: ..., source_repo: ...}
      Logger.debug(
        "[create_source_files] Processing file: #{file.path}, Input content size: #{byte_size(file.content || "")}"
      )

      # Get all repositories from target folders to associate with the source file
      repositories_to_associate =
        target_folders
        |> Enum.flat_map(fn folder ->
          Repobot.Repo.preload(folder, :repositories).repositories
        end)
        |> Enum.uniq_by(& &1.id)

      # Use the same approach as the import_file functionality in Repositories.Show
      # but import FROM the template repository (not from target repositories)
      attrs = %{
        name: Path.basename(file.path),
        content: file.content,
        target_path: file.path,
        organization_id: current_user.default_organization_id,
        # Use template repository as source instead of target repository
        source_repository_id: template_repo.id,
        read_only: template_repo.template,
        user_id: current_user.id
      }

      Logger.debug(
        "[create_source_files] Attrs passed to SourceFiles.import_file: #{inspect(Map.put(attrs, :content, "[CONTENT REDACTED]"))}"
      )

      case SourceFiles.import_file(attrs, repositories_to_associate) do
        {:ok, source_file} ->
          Logger.debug(
            "[create_source_files] DB Result for #{source_file.target_path}: Content size: #{byte_size(source_file.content || "")}, ID: #{source_file.id}"
          )

          # Associate the source file with all target folders (same as import_file in Repositories.Show)
          Enum.each(target_folders, fn folder ->
            case SourceFiles.add_source_file_to_folder(source_file, folder) do
              {:ok, _} ->
                Logger.debug(
                  "[create_source_files] Successfully associated source file #{source_file.target_path} with folder #{folder.name}"
                )

              # Note: add_source_file_to_folder always returns {:ok, source_file}
              # but we'll handle any potential future error cases gracefully
              other ->
                Logger.warning(
                  "[create_source_files] Unexpected result associating source file #{source_file.target_path} with folder #{folder.name}: #{inspect(other)}"
                )
            end
          end)

          {:cont, {:ok, [source_file | acc]}}

        {:error, reason} ->
          Logger.error(
            "[create_source_files] Error importing source file #{file.path}: #{inspect(reason)}"
          )

          {:halt, {:error, reason}}
      end
    end)
  end

  defp ensure_repository_is_template(repository, target_folders) do
    # Check if repository is already marked as template
    if repository.template do
      # Repository is already a template, but we still need to add it to target folders
      add_template_to_target_folders(repository, target_folders)
    else
      # Store the current folder before updating to template
      current_folder =
        if repository.folder_id do
          Repobot.Folders.get_folder!(repository.folder_id)
        else
          nil
        end

      # Update repository to be a template
      case Repositories.update_repository(repository, %{template: true}) do
        {:ok, updated_repository} ->
          # Collect all folders to add the template to
          folders_to_add =
            [current_folder | target_folders]
            |> Enum.reject(&is_nil/1)
            |> Enum.uniq_by(& &1.id)

          # Add the repository to all relevant folders
          add_template_to_target_folders(updated_repository, folders_to_add)

        {:error, reason} ->
          {:error, reason}
      end
    end
  end

  defp add_template_to_target_folders(repository, folders) do
    # Add the template repository to each target folder
    results =
      Enum.map(folders, fn folder ->
        case Repositories.add_template_folder(repository, folder) do
          {:ok, _} ->
            Logger.info(
              "Successfully added template repository #{repository.full_name} to folder #{folder.name}"
            )

            :ok

          {:error, reason} ->
            Logger.warning(
              "Failed to add template repository #{repository.full_name} to folder #{folder.name}: #{inspect(reason)}"
            )

            {:error, reason}
        end
      end)

    # Check if any operations failed
    failed_operations = Enum.filter(results, &match?({:error, _}, &1))

    if Enum.empty?(failed_operations) do
      {:ok, repository}
    else
      # Log the failures but still return success since the main template update worked
      Logger.warning(
        "Some folder associations failed, but template repository creation succeeded"
      )

      {:ok, repository}
    end
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <h2 class="text-2xl font-semibold text-slate-900 mb-4">Setup Summary</h2>
      <%= if @finalizing_progress < 100 do %>
        <div class="flex flex-col items-center justify-center py-12">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
          <p class="mt-4 text-slate-600">
            Setting up your template repository...
          </p>
          <%= if @finalizing_status do %>
            <p class="mt-2 text-sm text-slate-600">
              {@finalizing_status}
            </p>
          <% end %>
          <%= if @existing_files && length(@existing_files) > 0 do %>
            <div class="mt-4 bg-blue-50 border border-blue-200 rounded-lg p-4 w-full max-w-lg">
              <p class="text-sm text-blue-800">
                {length(@existing_files)} files already exist in the template:
              </p>
              <ul class="mt-2 text-sm text-blue-700 space-y-1">
                <%= for file <- @existing_files do %>
                  <li class="flex items-center gap-2">
                    <.icon name="hero-check-circle" class="w-4 h-4 text-blue-600" />
                    <span class="font-mono">{file.path}</span>
                  </li>
                <% end %>
              </ul>
            </div>
          <% end %>
          <%= if @new_files && length(@new_files) > 0 do %>
            <div class="mt-4 bg-emerald-50 border border-emerald-200 rounded-lg p-4 w-full max-w-lg">
              <p class="text-sm text-emerald-800">
                Adding {length(@new_files)} new files:
              </p>
              <ul class="mt-2 text-sm text-emerald-700 space-y-1">
                <%= for file <- @new_files do %>
                  <li class="flex items-center gap-2">
                    <.icon name="hero-plus-circle" class="w-4 h-4 text-emerald-600" />
                    <span class="font-mono">{file.path}</span>
                  </li>
                <% end %>
              </ul>
            </div>
          <% end %>
          <div class="w-full max-w-lg mt-6">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm text-slate-600">Progress</span>
              <span class="text-sm text-slate-600">{@finalizing_progress}%</span>
            </div>
            <div class="w-full bg-slate-200 rounded-full h-2">
              <div
                class="bg-indigo-600 h-2 rounded-full transition-all duration-200"
                style={"width: #{@finalizing_progress}%"}
              >
              </div>
            </div>
          </div>
        </div>
      <% else %>
        <div>
          <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h3 class="text-sm font-medium text-blue-800 mb-2">Setup Summary</h3>
            <ul class="text-sm text-blue-700 space-y-1">
              <li class="flex items-center gap-2">
                <.icon name="hero-check-circle" class="w-4 h-4 text-blue-600" /> Template repository:
                <span class="font-medium">
                  <%= if @finalized_template_repo do %>
                    {@finalized_template_repo.full_name}
                  <% else %>
                    {@state.template_repo.full_name}
                  <% end %>
                </span>
              </li>
              <li class="flex items-center gap-2">
                <.icon name="hero-check-circle" class="w-4 h-4 text-blue-600" /> Target repositories:
                <span class="font-medium">{length(@state.selected_repos)}</span>
                selected
              </li>
              <%= if Map.get(@state, :target_folders, []) |> Enum.any?() do %>
                <li class="flex items-center gap-2">
                  <.icon name="hero-check-circle" class="w-4 h-4 text-blue-600" /> Folders:
                  <span class="font-medium">
                    {Enum.map_join(@state.target_folders, ", ", & &1.name)}
                  </span>
                </li>
              <% end %>
            </ul>
          </div>

          <p class="text-slate-600 mb-6">
            Congratulations! Your template repository is ready. You can now:
          </p>

          <ul class="list-disc list-inside text-sm text-slate-600 mb-6 space-y-1">
            <li>View and manage your template files</li>
            <li>Add more files to the template</li>
            <li>Sync changes across repositories</li>
          </ul>

          <div class="flex items-center gap-4">
            <.btn
              :if={@finalized_template_repo}
              href={~p"/repositories/#{@finalized_template_repo}"}
              data-phx-link="redirect"
              data-phx-link-state="push"
              class="inline-flex items-center px-4 py-2"
              variant="primary"
            >
              Go to Template Repository
            </.btn>

            <.btn
              href={~p"/dashboard"}
              data-phx-link="redirect"
              data-phx-link-state="push"
              class="inline-flex items-center px-4 py-2"
              variant="outline"
            >
              Go to Dashboard
            </.btn>
          </div>

          <%= if is_nil(@finalized_template_repo) do %>
            <div class="mt-4 p-4 bg-amber-50 border border-amber-200 rounded-lg">
              <div class="flex items-start gap-2">
                <.icon name="hero-exclamation-triangle" class="w-5 h-5 text-amber-400 mt-0.5" />
                <p class="text-sm text-amber-800">
                  There was an issue finalizing the template repository. You can view your repositories from the dashboard.
                </p>
              </div>
            </div>
          <% end %>
        </div>
      <% end %>
    </div>
    """
  end
end
